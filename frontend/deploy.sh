#!/bin/bash

# 部署脚本
set -e

echo "🚀 开始部署 ww小岸前端..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_requirements() {
    log_info "检查部署环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    npm ci --only=production
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    npm run build
    log_success "项目构建完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    if npm run test --if-present; then
        log_success "测试通过"
    else
        log_warning "测试失败或未配置测试"
    fi
}

# 类型检查
type_check() {
    log_info "进行类型检查..."
    if npm run type-check --if-present; then
        log_success "类型检查通过"
    else
        log_warning "类型检查失败或未配置"
    fi
}

# 代码检查
lint_check() {
    log_info "进行代码检查..."
    if npm run lint --if-present; then
        log_success "代码检查通过"
    else
        log_warning "代码检查失败或未配置"
    fi
}

# Docker 部署
deploy_docker() {
    log_info "使用 Docker 部署..."
    
    # 构建镜像
    docker build -t ww-frontend:latest .
    
    # 停止旧容器
    docker stop ww-frontend || true
    docker rm ww-frontend || true
    
    # 启动新容器
    docker run -d \
        --name ww-frontend \
        --restart unless-stopped \
        -p 3000:3000 \
        -e NODE_ENV=production \
        ww-frontend:latest
    
    log_success "Docker 部署完成"
}

# PM2 部署
deploy_pm2() {
    log_info "使用 PM2 部署..."
    
    # 安装 PM2（如果未安装）
    if ! command -v pm2 &> /dev/null; then
        npm install -g pm2
    fi
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    log_success "PM2 部署完成"
}

# Vercel 部署
deploy_vercel() {
    log_info "使用 Vercel 部署..."
    
    # 安装 Vercel CLI（如果未安装）
    if ! command -v vercel &> /dev/null; then
        npm install -g vercel
    fi
    
    # 部署到 Vercel
    vercel --prod
    
    log_success "Vercel 部署完成"
}

# 清理缓存
clean_cache() {
    log_info "清理缓存..."
    rm -rf .next/cache
    npm cache clean --force
    log_success "缓存清理完成"
}

# 健康检查
health_check() {
    log_info "进行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    # 检查服务是否响应
    if curl -f http://localhost:3000/health &> /dev/null; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        exit 1
    fi
}

# 主函数
main() {
    local deploy_method=${1:-"build"}
    
    case $deploy_method in
        "docker")
            check_requirements
            install_dependencies
            run_tests
            type_check
            lint_check
            deploy_docker
            health_check
            ;;
        "pm2")
            check_requirements
            install_dependencies
            build_project
            run_tests
            type_check
            lint_check
            deploy_pm2
            health_check
            ;;
        "vercel")
            check_requirements
            install_dependencies
            run_tests
            type_check
            lint_check
            deploy_vercel
            ;;
        "build")
            check_requirements
            install_dependencies
            build_project
            run_tests
            type_check
            lint_check
            ;;
        "clean")
            clean_cache
            ;;
        *)
            echo "使用方法: $0 [docker|pm2|vercel|build|clean]"
            echo ""
            echo "选项:"
            echo "  docker  - 使用 Docker 部署"
            echo "  pm2     - 使用 PM2 部署"
            echo "  vercel  - 使用 Vercel 部署"
            echo "  build   - 仅构建项目"
            echo "  clean   - 清理缓存"
            exit 1
            ;;
    esac
    
    log_success "🎉 部署完成！"
}

# 执行主函数
main "$@"
