# ww小岸 - 现代化前端界面

基于 Next.js + TypeScript + Tailwind CSS 构建的现代化前端界面，采用苹果风格设计和玻璃拟态效果。

## ✨ 特性

- 🎨 **现代设计**: 苹果风格设计语言，玻璃拟态效果
- 📱 **响应式**: 完美适配移动端、平板和桌面端
- ⚡ **高性能**: 代码分割、懒加载、图片优化
- ♿ **无障碍**: WCAG 2.1 AA 标准合规
- 🌙 **主题支持**: 自动适配系统主题
- 🔧 **TypeScript**: 完整的类型安全
- 🎭 **动画**: 流畅的 Framer Motion 动画

## 🛠️ 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **动画**: Framer Motion
- **图标**: Lucide React
- **工具**: ESLint, Prettier

## 📦 安装

```bash
# 克隆项目
git clone <repository-url>
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 🚀 开发

### 目录结构

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理员页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 用户首页
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   ├── admin/            # 管理员组件
│   └── layout/           # 布局组件
├── lib/                  # 工具函数
├── hooks/                # 自定义Hooks
├── types/                # TypeScript类型定义
└── styles/               # 样式文件
```

### 核心组件

#### 玻璃拟态组件
```tsx
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass'

// 玻璃卡片
<GlassCard variant="strong" hover="all">
  内容
</GlassCard>

// 玻璃按钮
<GlassButton variant="strong" size="lg">
  按钮
</GlassButton>

// 玻璃输入框
<GlassInput
  label="标签"
  placeholder="占位符"
  leftIcon={<Icon />}
/>
```

#### 响应式布局
```tsx
import { ResponsiveLayout, ResponsiveGrid } from '@/components/layout/responsive-layout'

// 响应式容器
<ResponsiveLayout maxWidth="lg" padding="md">
  内容
</ResponsiveLayout>

// 响应式网格
<ResponsiveGrid cols={{ default: 1, md: 2, lg: 3 }}>
  <div>项目1</div>
  <div>项目2</div>
  <div>项目3</div>
</ResponsiveGrid>
```

#### 优化图片
```tsx
import { OptimizedImage, GroupAvatar } from '@/components/ui/optimized-image'

// 优化图片
<OptimizedImage
  src="/image.jpg"
  alt="描述"
  width={400}
  height={300}
  priority
/>

// 群头像
<GroupAvatar groupNumber="123456789" size={60} />
```

### 样式系统

#### 颜色变量
```css
:root {
  --primary: #007aff;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}
```

#### 玻璃拟态类
```css
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}
```

#### 动画类
```css
.animate-fade-in { animation: fadeIn 0.6s ease-out; }
.animate-slide-up { animation: slideUp 0.6s ease-out; }
.animate-float { animation: float 6s ease-in-out infinite; }
```

## 🎨 设计系统

### 颜色
- **主色**: #007aff (iOS蓝)
- **成功**: #34c759 (iOS绿)
- **警告**: #ff9500 (iOS橙)
- **错误**: #ff3b30 (iOS红)

### 字体
- **主字体**: Inter, -apple-system, BlinkMacSystemFont
- **等宽字体**: SF Mono, Monaco, Consolas

### 间距
- **xs**: 0.25rem (4px)
- **sm**: 0.5rem (8px)
- **md**: 1rem (16px)
- **lg**: 1.5rem (24px)
- **xl**: 2rem (32px)

### 圆角
- **sm**: 0.375rem (6px)
- **md**: 0.75rem (12px)
- **lg**: 1rem (16px)
- **xl**: 1.5rem (24px)

## 📱 响应式断点

- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## ♿ 无障碍性

### 键盘导航
- 所有交互元素支持键盘访问
- 合理的 Tab 顺序
- 焦点指示器清晰可见

### 屏幕阅读器
- 语义化 HTML 结构
- 适当的 ARIA 标签
- 实时区域更新公告

### 颜色对比度
- 文本对比度符合 WCAG AA 标准
- 支持高对比度模式
- 不依赖颜色传达信息

### 动画偏好
- 支持 `prefers-reduced-motion`
- 可选择性禁用动画

## 🔧 配置

### 环境变量
```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=ww小岸
```

### Tailwind 配置
```js
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#007aff',
        glass: 'rgba(255, 255, 255, 0.1)',
      },
      backdropBlur: {
        xs: '2px',
      },
    },
  },
}
```

## 📋 脚本命令

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

## 🚀 部署

### Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

### Docker 部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🐛 故障排除

### 常见问题

1. **样式不生效**
   - 检查 Tailwind CSS 配置
   - 确认类名拼写正确
   - 清除浏览器缓存

2. **图片加载失败**
   - 检查图片路径
   - 确认 Next.js 图片配置
   - 验证图片格式支持

3. **动画卡顿**
   - 检查 GPU 加速
   - 减少同时运行的动画
   - 使用 `will-change` 属性

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请联系开发团队或提交 Issue。
