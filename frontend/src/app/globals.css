@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  /* 苹果风格颜色系统 */
  --background: #ffffff;
  --foreground: #1d1d1f;
  --muted: #f5f5f7;
  --muted-foreground: #86868b;
  --border: #d2d2d7;
  --input: #ffffff;
  --primary: #007aff;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f7;
  --secondary-foreground: #1d1d1f;
  --accent: #007aff;
  --accent-foreground: #ffffff;
  --destructive: #ff3b30;
  --destructive-foreground: #ffffff;
  --ring: #007aff;

  /* 玻璃拟态效果变量 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-strong-bg: rgba(255, 255, 255, 0.15);
  --glass-strong-border: rgba(255, 255, 255, 0.25);
  --glass-dark-bg: rgba(0, 0, 0, 0.2);
  --glass-dark-border: rgba(255, 255, 255, 0.1);

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

@theme inline {
  /* 颜色系统 */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);

  /* 玻璃拟态颜色 */
  --color-glass: var(--glass-bg);
  --color-glass-border: var(--glass-border);
  --color-glass-strong: var(--glass-strong-bg);
  --color-glass-strong-border: var(--glass-strong-border);
  --color-glass-dark: var(--glass-dark-bg);
  --color-glass-dark-border: var(--glass-dark-border);

  /* 字体系统 */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
  --radius-3xl: 3rem;

  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --muted: #1c1c1e;
    --muted-foreground: #8e8e93;
    --border: #38383a;
    --input: #1c1c1e;
    --primary: #0a84ff;
    --primary-foreground: #ffffff;
    --secondary: #1c1c1e;
    --secondary-foreground: #ffffff;
    --accent: #0a84ff;
    --accent-foreground: #ffffff;
    --destructive: #ff453a;
    --destructive-foreground: #ffffff;
    --ring: #0a84ff;
  }
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  line-height: 1.5;
  min-height: 100vh;
}

/* 玻璃拟态效果类 */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: var(--glass-strong-bg);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--glass-strong-border);
}

.glass-dark {
  background: var(--glass-dark-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-dark-border);
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 动画工具类 */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 响应式设计优化 */
@media (max-width: 640px) {
  /* 移动端优化 */
  .glass, .glass-strong, .glass-dark {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  /* 减少移动端的模糊效果以提升性能 */
  .glass {
    background: rgba(255, 255, 255, 0.15);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.2);
  }

  /* 移动端字体大小调整 */
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }

  /* 移动端间距调整 */
  .space-y-8 > * + * { margin-top: 1.5rem; }
  .space-y-6 > * + * { margin-top: 1rem; }
  .space-y-4 > * + * { margin-top: 0.75rem; }

  /* 移动端按钮优化 */
  button, .glass-button {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端输入框优化 */
  input, textarea, select {
    font-size: 16px; /* 防止iOS缩放 */
  }
}

@media (max-width: 768px) {
  /* 平板端优化 */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 网格布局调整 */
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  /* 桌面端优化 */
  .glass, .glass-strong, .glass-dark {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
  }

  /* 桌面端悬停效果增强 */
  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:-translate-y-3:hover {
    transform: translateY(-0.75rem);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果，使用点击效果 */
  .hover\:bg-accent:hover {
    background-color: initial;
  }

  .hover\:-translate-y-2:hover {
    transform: initial;
  }

  .hover\:scale-\[1\.02\]:hover {
    transform: initial;
  }

  /* 增加点击反馈 */
  button:active, .glass-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .glass, .glass-strong, .glass-dark {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }

  .text-white\/70 {
    color: rgba(255, 255, 255, 0.9);
  }

  .text-white\/60 {
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-spin,
  .animate-pulse,
  .animate-float {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .glass, .glass-strong, .glass-dark {
    background: white;
    color: black;
    border: 1px solid black;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }

  .text-white {
    color: black !important;
  }

  .bg-gradient-to-br,
  .bg-gradient-to-r {
    background: white !important;
  }

  button {
    border: 1px solid black;
    background: white;
    color: black;
  }
}

/* 焦点可见性增强 */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--ring);
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  border-radius: 4px;
  text-decoration: none;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}
