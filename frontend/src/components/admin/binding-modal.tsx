"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { Receipt, Users, Tag, Calendar, User, Save, X } from "lucide-react"
import { <PERSON><PERSON>, ModalContent, ModalFooter } from "@/components/ui/modal"
import { GlassInput, GlassButton } from "@/components/ui/glass"
import { isValidGroupNumber } from "@/lib/utils"
import type { Order, BindingData } from "@/types"

export interface BindingModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: BindingData) => Promise<void>
  editingData?: Order | null
  isLoading?: boolean
  error?: string | null
}

export function BindingModal({
  isOpen,
  onClose,
  onSubmit,
  editingData,
  isLoading = false,
  error
}: BindingModalProps) {
  const [formData, setFormData] = React.useState<BindingData>({
    orderNumber: "",
    groupNumber: "",
    skuId: "",
    owner: "",
    expirationTimestamp: "",
    remainingDays: 0,
    isActive: false
  })
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({})

  // 当编辑数据变化时更新表单
  React.useEffect(() => {
    if (editingData) {
      setFormData({
        orderNumber: editingData.orderNumber,
        groupNumber: editingData.groupNumber,
        skuId: editingData.skuId,
        owner: editingData.owner || "",
        expirationTimestamp: editingData.expirationDate.includes('永久') 
          ? "" 
          : new Date(editingData.expirationDate).toISOString().slice(0, 16),
        remainingDays: editingData.remainingDays || 0,
        isActive: editingData.isActive
      })
    } else {
      setFormData({
        orderNumber: "",
        groupNumber: "",
        skuId: "",
        owner: "",
        expirationTimestamp: "",
        remainingDays: 0,
        isActive: false
      })
    }
    setFormErrors({})
  }, [editingData, isOpen])

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.orderNumber.trim()) {
      errors.orderNumber = "请输入订单号"
    }
    
    if (!formData.groupNumber.trim()) {
      errors.groupNumber = "请输入群号"
    } else if (!isValidGroupNumber(formData.groupNumber)) {
      errors.groupNumber = "请输入有效的QQ群号（6-12位数字）"
    }
    
    if (!formData.skuId?.trim()) {
      errors.skuId = "请输入SKU ID"
    }
    
    if (!formData.expirationTimestamp && !formData.remainingDays) {
      errors.expirationTimestamp = "请设置到期时间或剩余天数"
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error("提交失败:", error)
    }
  }

  const handleInputChange = (field: keyof BindingData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const value = e.target.type === 'checkbox' 
      ? (e.target as HTMLInputElement).checked
      : e.target.value

    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }))
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose()
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={editingData ? "编辑绑定" : "添加新绑定"}
      description={editingData ? "修改现有绑定信息" : "创建新的订单绑定"}
      size="lg"
      closeOnOverlayClick={!isLoading}
      closeOnEscape={!isLoading}
    >
      <ModalContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 订单号 */}
            <GlassInput
              label="订单号"
              type="text"
              placeholder="请输入爱发电订单号"
              value={formData.orderNumber}
              onChange={handleInputChange("orderNumber")}
              leftIcon={<Receipt className="w-5 h-5" />}
              error={formErrors.orderNumber}
              disabled={isLoading}
              required
            />

            {/* 群号 */}
            <GlassInput
              label="QQ群号"
              type="text"
              placeholder="请输入QQ群号"
              value={formData.groupNumber}
              onChange={handleInputChange("groupNumber")}
              leftIcon={<Users className="w-5 h-5" />}
              error={formErrors.groupNumber}
              disabled={isLoading}
              required
            />

            {/* SKU ID */}
            <GlassInput
              label="SKU ID"
              type="text"
              placeholder="请输入SKU ID"
              value={formData.skuId || ""}
              onChange={handleInputChange("skuId")}
              leftIcon={<Tag className="w-5 h-5" />}
              error={formErrors.skuId}
              disabled={isLoading}
              required
            />

            {/* 所有者 */}
            <GlassInput
              label="所有者"
              type="text"
              placeholder="请输入所有者名称（可选）"
              value={formData.owner || ""}
              onChange={handleInputChange("owner")}
              leftIcon={<User className="w-5 h-5" />}
              disabled={isLoading}
            />

            {/* 到期时间 */}
            <GlassInput
              label="到期时间"
              type="datetime-local"
              value={formData.expirationTimestamp || ""}
              onChange={handleInputChange("expirationTimestamp")}
              leftIcon={<Calendar className="w-5 h-5" />}
              error={formErrors.expirationTimestamp}
              disabled={isLoading}
            />

            {/* 剩余天数 */}
            <GlassInput
              label="剩余天数"
              type="number"
              placeholder="0"
              min="0"
              value={formData.remainingDays?.toString() || "0"}
              onChange={handleInputChange("remainingDays")}
              disabled={isLoading}
            />
          </div>

          {/* 激活状态 */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive || false}
              onChange={handleInputChange("isActive")}
              disabled={isLoading}
              className="w-4 h-4 text-blue-600 bg-transparent border-2 border-white/30 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="isActive" className="text-white/90 font-medium">
              立即激活此绑定
            </label>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-4 bg-red-500/20 border border-red-500/30 rounded-2xl text-red-200"
            >
              {error}
            </motion.div>
          )}
        </form>
      </ModalContent>

      <ModalFooter>
        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <GlassButton
            type="button"
            variant="subtle"
            onClick={handleClose}
            disabled={isLoading}
            leftIcon={<X className="w-4 h-4" />}
            className="flex-1"
          >
            取消
          </GlassButton>
          
          <GlassButton
            type="submit"
            variant="strong"
            onClick={handleSubmit}
            loading={isLoading}
            className="flex-1 bg-gradient-to-r from-green-500/20 to-blue-600/20 hover:from-green-500/30 hover:to-blue-600/30"
            rightIcon={!isLoading && <Save className="w-4 h-4" />}
          >
            {isLoading ? "保存中..." : editingData ? "更新绑定" : "创建绑定"}
          </GlassButton>
        </div>
      </ModalFooter>
    </Modal>
  )
}
