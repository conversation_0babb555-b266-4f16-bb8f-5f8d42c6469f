"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { 
  Database, 
  Plus, 
  Search, 
  Filter, 
  Download,
  ArrowDown,
  ArrowUp,
  RefreshCw
} from "lucide-react"
import { GlassCard, GlassButton, GlassInput } from "@/components/ui/glass"
import { BindingTable } from "./binding-table"
import { BindingModal } from "./binding-modal"
import type { Order, BindingData } from "@/types"

export interface AdminPanelProps {
  bindings: Order[]
  isLoading?: boolean
  onRefresh: () => void
  onAddBinding: (data: BindingData) => Promise<void>
  onEditBinding: (data: BindingData & { originalOrderNumber: string; originalSkuId: string }) => Promise<void>
  onDeleteBinding: (orderNumber: string, skuId: string) => Promise<void>
  onUpgradeBinding: (id: number) => Promise<void>
}

export function AdminPanel({
  bindings,
  isLoading = false,
  onRefresh,
  onAddBinding,
  onEditBinding,
  onDeleteBinding,
  onUpgradeBinding
}: AdminPanelProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = React.useState(1)
  const [itemsPerPage] = React.useState(10)
  const [showBindingModal, setShowBindingModal] = React.useState(false)
  const [editingBinding, setEditingBinding] = React.useState<Order | null>(null)
  const [modalLoading, setModalLoading] = React.useState(false)
  const [modalError, setModalError] = React.useState<string | null>(null)

  // 过滤和排序数据
  const filteredBindings = React.useMemo(() => {
    let filtered = bindings.filter(binding => 
      binding.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      binding.groupNumber.includes(searchQuery) ||
      (binding.owner && binding.owner.toLowerCase().includes(searchQuery.toLowerCase()))
    )

    // 排序
    filtered.sort((a, b) => {
      const dateA = new Date(a.bindTime || 0).getTime()
      const dateB = new Date(b.bindTime || 0).getTime()
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
    })

    return filtered
  }, [bindings, searchQuery, sortOrder])

  // 分页数据
  const paginatedBindings = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredBindings.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredBindings, currentPage, itemsPerPage])

  const totalPages = Math.ceil(filteredBindings.length / itemsPerPage)

  // 处理添加绑定
  const handleAddBinding = async (data: BindingData) => {
    setModalLoading(true)
    setModalError(null)
    
    try {
      await onAddBinding(data)
      setShowBindingModal(false)
      setEditingBinding(null)
    } catch (error) {
      setModalError(error instanceof Error ? error.message : '操作失败')
    } finally {
      setModalLoading(false)
    }
  }

  // 处理编辑绑定
  const handleEditBinding = async (data: BindingData) => {
    if (!editingBinding) return
    
    setModalLoading(true)
    setModalError(null)
    
    try {
      await onEditBinding({
        ...data,
        originalOrderNumber: editingBinding.orderNumber,
        originalSkuId: editingBinding.skuId
      })
      setShowBindingModal(false)
      setEditingBinding(null)
    } catch (error) {
      setModalError(error instanceof Error ? error.message : '操作失败')
    } finally {
      setModalLoading(false)
    }
  }

  // 打开编辑模态框
  const handleEditClick = (binding: Order) => {
    setEditingBinding(binding)
    setShowBindingModal(true)
  }

  // 关闭模态框
  const handleCloseModal = () => {
    setShowBindingModal(false)
    setEditingBinding(null)
    setModalError(null)
  }

  // 切换排序
  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'desc' ? 'asc' : 'desc')
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* 顶部导航栏 */}
      <GlassCard variant="strong" hover="lift" className="p-6">
        <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 glass rounded-xl flex items-center justify-center">
              <Database className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-white">绑定管理</h2>
              <p className="text-white/70">管理所有订单绑定信息</p>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            {/* 排序控制 */}
            <div className="glass rounded-2xl p-1 flex items-center">
              <span className="text-sm text-white/80 ml-3 mr-2">排序:</span>
              <GlassButton
                variant="subtle"
                size="sm"
                onClick={toggleSortOrder}
                leftIcon={sortOrder === 'desc' ? <ArrowDown className="w-4 h-4" /> : <ArrowUp className="w-4 h-4" />}
              >
                {sortOrder === 'desc' ? '降序' : '升序'}
              </GlassButton>
            </div>

            {/* 添加绑定按钮 */}
            <GlassButton
              variant="strong"
              onClick={() => setShowBindingModal(true)}
              className="bg-gradient-to-r from-green-500/20 to-emerald-600/20 hover:from-green-500/30 hover:to-emerald-600/30"
              leftIcon={<Plus className="w-5 h-5" />}
            >
              添加新绑定
            </GlassButton>
          </div>
        </div>
      </GlassCard>

      {/* 搜索和筛选区域 */}
      <GlassCard variant="glass" hover="lift" className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* 搜索框 */}
          <div className="flex-1">
            <GlassInput
              placeholder="搜索群号、订单号或用户名..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="w-5 h-5" />}
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <GlassButton
              variant="subtle"
              onClick={onRefresh}
              disabled={isLoading}
              leftIcon={<RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />}
            >
              刷新
            </GlassButton>
            <GlassButton
              variant="subtle"
              leftIcon={<Filter className="w-4 h-4" />}
            >
              筛选
            </GlassButton>
            <GlassButton
              variant="subtle"
              leftIcon={<Download className="w-4 h-4" />}
            >
              导出
            </GlassButton>
          </div>
        </div>
      </GlassCard>

      {/* 数据表格 */}
      <BindingTable
        bindings={paginatedBindings}
        isLoading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={filteredBindings.length}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
        onEdit={handleEditClick}
        onDelete={onDeleteBinding}
        onUpgrade={onUpgradeBinding}
      />

      {/* 绑定模态框 */}
      <BindingModal
        isOpen={showBindingModal}
        onClose={handleCloseModal}
        onSubmit={editingBinding ? handleEditBinding : handleAddBinding}
        editingData={editingBinding}
        isLoading={modalLoading}
        error={modalError}
      />
    </motion.div>
  )
}
