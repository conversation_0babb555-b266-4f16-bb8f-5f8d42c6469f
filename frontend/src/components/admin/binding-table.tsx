"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { 
  Edit, 
  Trash2, 
  TrendingUp, 
  Package, 
  ChevronLeft, 
  ChevronRight,
  Users,
  Clock,
  Tag
} from "lucide-react"
import { GlassCard, GlassButton } from "@/components/ui/glass"
import { formatDate, formatRemainingDays, escapeHtml } from "@/lib/utils"
import type { Order } from "@/types"

export interface BindingTableProps {
  bindings: Order[]
  isLoading?: boolean
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onEdit: (binding: Order) => void
  onDelete: (orderNumber: string, skuId: string) => Promise<void>
  onUpgrade: (id: number) => Promise<void>
}

export function BindingTable({
  bindings,
  isLoading = false,
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onEdit,
  onDelete,
  onUpgrade
}: BindingTableProps) {
  const [deletingItems, setDeletingItems] = React.useState<Set<string>>(new Set())
  const [upgradingItems, setUpgradingItems] = React.useState<Set<number>>(new Set())

  // 处理删除
  const handleDelete = async (orderNumber: string, skuId: string, skuType: string) => {
    const confirmed = confirm(`确定要删除订单 "${orderNumber}" (${skuType}) 吗？此操作不可撤销。`)
    if (!confirmed) return

    const key = `${orderNumber}-${skuId}`
    setDeletingItems(prev => new Set(prev).add(key))

    try {
      await onDelete(orderNumber, skuId)
    } catch (error) {
      console.error('删除失败:', error)
    } finally {
      setDeletingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(key)
        return newSet
      })
    }
  }

  // 处理升级
  const handleUpgrade = async (id: number, orderNumber: string) => {
    const confirmed = confirm(`确定要将订单 "${orderNumber}" 升级吗？原订单将被删除，剩余价值将转换为新档位时长。此操作不可撤销。`)
    if (!confirmed) return

    setUpgradingItems(prev => new Set(prev).add(id))

    try {
      await onUpgrade(id)
    } catch (error) {
      console.error('升级失败:', error)
    } finally {
      setUpgradingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  // 计算分页信息
  const startItem = (currentPage - 1) * itemsPerPage + 1
  const endItem = Math.min(currentPage * itemsPerPage, totalItems)

  if (isLoading) {
    return (
      <GlassCard variant="glass" className="p-8">
        <div className="text-center text-white/60">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-white/30 border-t-white mx-auto mb-4"></div>
          <p>加载中，请稍候...</p>
        </div>
      </GlassCard>
    )
  }

  if (bindings.length === 0) {
    return (
      <GlassCard variant="glass" className="p-8">
        <div className="text-center text-white/60">
          <Package className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-semibold text-white mb-2">暂无数据</h3>
          <p>还没有任何绑定记录</p>
        </div>
      </GlassCard>
    )
  }

  return (
    <div className="space-y-6">
      {/* 桌面端表格 */}
      <div className="hidden lg:block">
        <GlassCard variant="glass" className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="glass-dark">
                <tr className="text-white/90">
                  <th className="px-6 py-4 text-left text-sm font-semibold">序号</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">群信息</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">订单详情</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">档位</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">状态</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">到期时间</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold">操作</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {bindings.map((binding, index) => (
                  <TableRow
                    key={`${binding.orderNumber}-${binding.skuId}`}
                    binding={binding}
                    index={startItem + index}
                    onEdit={() => onEdit(binding)}
                    onDelete={() => handleDelete(binding.orderNumber, binding.skuId, binding.skuType)}
                    onUpgrade={() => handleUpgrade(binding.id, binding.orderNumber)}
                    isDeleting={deletingItems.has(`${binding.orderNumber}-${binding.skuId}`)}
                    isUpgrading={upgradingItems.has(binding.id)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </GlassCard>
      </div>

      {/* 移动端卡片视图 */}
      <div className="lg:hidden space-y-4">
        {bindings.map((binding, index) => (
          <MobileCard
            key={`${binding.orderNumber}-${binding.skuId}`}
            binding={binding}
            index={startItem + index}
            onEdit={() => onEdit(binding)}
            onDelete={() => handleDelete(binding.orderNumber, binding.skuId, binding.skuType)}
            onUpgrade={() => handleUpgrade(binding.id, binding.orderNumber)}
            isDeleting={deletingItems.has(`${binding.orderNumber}-${binding.skuId}`)}
            isUpgrading={upgradingItems.has(binding.id)}
          />
        ))}
      </div>

      {/* 分页控制 */}
      {totalPages > 1 && (
        <GlassCard variant="glass" className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            {/* 分页信息 */}
            <div className="text-white/80 text-sm">
              显示 <span className="font-semibold text-white">{startItem}-{endItem}</span> 条，共 <span className="font-semibold text-white">{totalItems}</span> 条
            </div>

            {/* 分页按钮 */}
            <div className="flex items-center space-x-2">
              <GlassButton
                variant="subtle"
                size="sm"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                leftIcon={<ChevronLeft className="w-4 h-4" />}
              >
                上一页
              </GlassButton>

              <div className="flex space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }

                  return (
                    <GlassButton
                      key={pageNum}
                      variant={currentPage === pageNum ? "strong" : "subtle"}
                      size="sm"
                      onClick={() => onPageChange(pageNum)}
                      className={currentPage === pageNum ? "bg-blue-500/30" : ""}
                    >
                      {pageNum}
                    </GlassButton>
                  )
                })}
              </div>

              <GlassButton
                variant="subtle"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                rightIcon={<ChevronRight className="w-4 h-4" />}
              >
                下一页
              </GlassButton>
            </div>
          </div>
        </GlassCard>
      )}
    </div>
  )
}

// 表格行组件
interface TableRowProps {
  binding: Order
  index: number
  onEdit: () => void
  onDelete: () => void
  onUpgrade: () => void
  isDeleting: boolean
  isUpgrading: boolean
}

function TableRow({
  binding,
  index,
  onEdit,
  onDelete,
  onUpgrade,
  isDeleting,
  isUpgrading
}: TableRowProps) {
  const groupAvatarUrl = `https://p.qlogo.cn/gh/${binding.groupNumber}/${binding.groupNumber}/40`
  const remainingDaysText = formatRemainingDays(binding.expirationDate)

  // 状态样式
  let statusClass = 'bg-yellow-500/20 text-yellow-200 border-yellow-500/30'
  let statusText = '待激活'

  if (binding.isActive) {
    if (remainingDaysText === '永久') {
      statusClass = 'bg-green-500/20 text-green-200 border-green-500/30'
      statusText = '永久有效'
    } else if (remainingDaysText === '已过期') {
      statusClass = 'bg-red-500/20 text-red-200 border-red-500/30'
      statusText = '已过期'
    } else {
      statusClass = 'bg-green-500/20 text-green-200 border-green-500/30'
      statusText = '正常'
    }
  }

  return (
    <motion.tr
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="hover:bg-white/5 transition-colors"
    >
      <td className="px-6 py-4 text-white/90 font-medium">#{index}</td>

      <td className="px-6 py-4">
        <div className="flex items-center space-x-3">
          <img
            src={groupAvatarUrl}
            alt="群头像"
            className="h-10 w-10 rounded-xl object-cover border border-white/20"
            onError={(e) => {
              e.currentTarget.src = 'https://placehold.co/40x40/667eea/ffffff?text=群'
            }}
          />
          <div>
            <div className="font-medium text-white">{binding.groupNumber}</div>
            <div className="text-sm text-white/60 flex items-center">
              <Users className="w-3 h-3 mr-1" />
              群聊
            </div>
          </div>
        </div>
      </td>

      <td className="px-6 py-4">
        <div>
          <div className="font-medium text-white">{escapeHtml(binding.orderNumber)}</div>
          <div className="text-sm text-white/60">{binding.owner || '系统'}</div>
        </div>
      </td>

      <td className="px-6 py-4">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-xl text-xs font-medium glass border border-white/20 text-white">
          <Tag className="w-3 h-3 mr-1" />
          {binding.skuType}
        </span>
      </td>

      <td className="px-6 py-4">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-xl text-xs font-medium border ${statusClass}`}>
          {statusText}
        </span>
      </td>

      <td className="px-6 py-4">
        <div>
          <div className="font-medium text-white">{remainingDaysText}</div>
          <div className="text-sm text-white/60 flex items-center">
            <Clock className="w-3 h-3 mr-1" />
            {binding.bindTime ? formatDate(binding.bindTime) : '-'}
          </div>
        </div>
      </td>

      <td className="px-6 py-4">
        <div className="flex items-center space-x-2">
          <GlassButton
            variant="subtle"
            size="sm"
            onClick={onEdit}
            leftIcon={<Edit className="w-3 h-3" />}
          >
            编辑
          </GlassButton>

          <GlassButton
            variant="subtle"
            size="sm"
            onClick={onUpgrade}
            loading={isUpgrading}
            leftIcon={!isUpgrading && <TrendingUp className="w-3 h-3" />}
            className="text-blue-200 hover:text-blue-100"
          >
            升级
          </GlassButton>

          <GlassButton
            variant="subtle"
            size="sm"
            onClick={onDelete}
            loading={isDeleting}
            leftIcon={!isDeleting && <Trash2 className="w-3 h-3" />}
            className="text-red-200 hover:text-red-100"
          >
            删除
          </GlassButton>
        </div>
      </td>
    </motion.tr>
  )
}

// 移动端卡片组件
interface MobileCardProps {
  binding: Order
  index: number
  onEdit: () => void
  onDelete: () => void
  onUpgrade: () => void
  isDeleting: boolean
  isUpgrading: boolean
}

function MobileCard({
  binding,
  index,
  onEdit,
  onDelete,
  onUpgrade,
  isDeleting,
  isUpgrading
}: MobileCardProps) {
  const groupAvatarUrl = `https://p.qlogo.cn/gh/${binding.groupNumber}/${binding.groupNumber}/60`
  const remainingDaysText = formatRemainingDays(binding.expirationDate)

  // 状态样式
  let statusClass = 'bg-yellow-500/20 text-yellow-200 border-yellow-500/30'
  let statusText = '待激活'

  if (binding.isActive) {
    if (remainingDaysText === '永久') {
      statusClass = 'bg-green-500/20 text-green-200 border-green-500/30'
      statusText = '永久有效'
    } else if (remainingDaysText === '已过期') {
      statusClass = 'bg-red-500/20 text-red-200 border-red-500/30'
      statusText = '已过期'
    } else {
      statusClass = 'bg-green-500/20 text-green-200 border-green-500/30'
      statusText = '正常'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass rounded-2xl p-6 hover:-translate-y-1 hover:shadow-2xl transition-all duration-300"
    >
      {/* 头部信息 */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <img
            src={groupAvatarUrl}
            alt="群头像"
            className="h-12 w-12 rounded-xl object-cover border-2 border-white/20"
            onError={(e) => {
              e.currentTarget.src = 'https://placehold.co/48x48/667eea/ffffff?text=群'
            }}
          />
          <div>
            <h4 className="font-semibold text-white">#{index}</h4>
            <p className="text-white/70 text-sm">群 {binding.groupNumber}</p>
          </div>
        </div>

        <span className={`px-3 py-1 rounded-xl text-xs font-medium border ${statusClass}`}>
          {statusText}
        </span>
      </div>

      {/* 详细信息 */}
      <div className="space-y-3 mb-4">
        <div className="flex justify-between items-center">
          <span className="text-white/60 text-sm">订单号:</span>
          <span className="text-white font-medium">{escapeHtml(binding.orderNumber)}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-white/60 text-sm">档位:</span>
          <span className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium glass border border-white/20 text-white">
            <Tag className="w-3 h-3 mr-1" />
            {binding.skuType}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-white/60 text-sm">剩余时间:</span>
          <span className="text-white font-medium">{remainingDaysText}</span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-white/60 text-sm">绑定时间:</span>
          <span className="text-white/80 text-sm">
            {binding.bindTime ? formatDate(binding.bindTime) : '-'}
          </span>
        </div>

        {binding.owner && (
          <div className="flex justify-between items-center">
            <span className="text-white/60 text-sm">所有者:</span>
            <span className="text-white/80 text-sm">{binding.owner}</span>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-2 pt-4 border-t border-white/10">
        <GlassButton
          variant="subtle"
          size="sm"
          onClick={onEdit}
          leftIcon={<Edit className="w-3 h-3" />}
          className="flex-1"
        >
          编辑
        </GlassButton>

        <GlassButton
          variant="subtle"
          size="sm"
          onClick={onUpgrade}
          loading={isUpgrading}
          leftIcon={!isUpgrading && <TrendingUp className="w-3 h-3" />}
          className="flex-1 text-blue-200 hover:text-blue-100"
        >
          升级
        </GlassButton>

        <GlassButton
          variant="subtle"
          size="sm"
          onClick={onDelete}
          loading={isDeleting}
          leftIcon={!isDeleting && <Trash2 className="w-3 h-3" />}
          className="flex-1 text-red-200 hover:text-red-100"
        >
          删除
        </GlassButton>
      </div>
    </motion.div>
  )
}
