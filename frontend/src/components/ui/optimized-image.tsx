"use client"

import * as React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

export interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  placeholder?: "blur" | "empty"
  blurDataURL?: string
  sizes?: string
  quality?: number
  loading?: "lazy" | "eager"
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  placeholder = "empty",
  blurDataURL,
  sizes,
  quality = 75,
  loading = "lazy",
  onLoad,
  onError,
  fallbackSrc
}: OptimizedImageProps) {
  const [imgSrc, setImgSrc] = React.useState(src)
  const [isLoading, setIsLoading] = React.useState(true)
  const [hasError, setHasError] = React.useState(false)

  const handleLoad = React.useCallback(() => {
    setIsLoading(false)
    onLoad?.()
  }, [onLoad])

  const handleError = React.useCallback(() => {
    setHasError(true)
    setIsLoading(false)
    if (fallbackSrc && imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc)
      setHasError(false)
      setIsLoading(true)
    }
    onError?.()
  }, [fallbackSrc, imgSrc, onError])

  // 重置状态当src改变时
  React.useEffect(() => {
    setImgSrc(src)
    setIsLoading(true)
    setHasError(false)
  }, [src])

  if (hasError && !fallbackSrc) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-muted text-muted-foreground rounded-lg",
          className
        )}
        style={{ width, height }}
        role="img"
        aria-label={`图片加载失败: ${alt}`}
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    )
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <div
          className="absolute inset-0 bg-muted animate-pulse rounded-lg"
          aria-hidden="true"
        />
      )}
      
      <Image
        src={imgSrc}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        sizes={sizes}
        quality={quality}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      />
    </div>
  )
}

// 懒加载图片组件
export interface LazyImageProps extends OptimizedImageProps {
  threshold?: number
  rootMargin?: string
}

export function LazyImage({
  threshold = 0.1,
  rootMargin = "50px",
  ...props
}: LazyImageProps) {
  const [isInView, setIsInView] = React.useState(false)
  const imgRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        threshold,
        rootMargin
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [threshold, rootMargin])

  return (
    <div ref={imgRef} className={props.className}>
      {isInView ? (
        <OptimizedImage {...props} />
      ) : (
        <div
          className="bg-muted animate-pulse rounded-lg"
          style={{ width: props.width, height: props.height }}
          aria-label={`正在加载图片: ${props.alt}`}
        />
      )}
    </div>
  )
}

// 头像组件（专门优化的）
export interface AvatarImageProps {
  src: string
  alt: string
  size?: number
  className?: string
  fallbackText?: string
}

export function AvatarImage({
  src,
  alt,
  size = 40,
  className,
  fallbackText
}: AvatarImageProps) {
  const [hasError, setHasError] = React.useState(false)

  const handleError = () => {
    setHasError(true)
  }

  if (hasError) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold rounded-xl",
          className
        )}
        style={{ width: size, height: size }}
        role="img"
        aria-label={alt}
      >
        {fallbackText || alt.charAt(0).toUpperCase()}
      </div>
    )
  }

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={size}
      height={size}
      className={cn("rounded-xl object-cover", className)}
      onError={handleError}
      quality={90}
      sizes={`${size}px`}
    />
  )
}

// 群头像组件
export interface GroupAvatarProps {
  groupNumber: string
  size?: number
  className?: string
}

export function GroupAvatar({
  groupNumber,
  size = 40,
  className
}: GroupAvatarProps) {
  const avatarUrl = `https://p.qlogo.cn/gh/${groupNumber}/${groupNumber}/${size}`
  const fallbackUrl = `https://placehold.co/${size}x${size}/667eea/ffffff?text=群`

  return (
    <AvatarImage
      src={avatarUrl}
      alt={`群 ${groupNumber} 头像`}
      size={size}
      className={className}
      fallbackText="群"
    />
  )
}
