"use client"

import * as React from "react"

// 性能监控Hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState<{
    renderTime: number
    memoryUsage?: number
    fps?: number
  }>({
    renderTime: 0
  })

  React.useEffect(() => {
    const startTime = performance.now()

    // 监控渲染时间
    const measureRenderTime = () => {
      const endTime = performance.now()
      setMetrics(prev => ({
        ...prev,
        renderTime: endTime - startTime
      }))
    }

    // 监控内存使用（如果支持）
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // MB
        }))
      }
    }

    // 监控FPS
    let frameCount = 0
    let lastTime = startTime
    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({
          ...prev,
          fps: Math.round((frameCount * 1000) / (currentTime - lastTime))
        }))
        frameCount = 0
        lastTime = currentTime
      }
      requestAnimationFrame(measureFPS)
    }

    measureRenderTime()
    measureMemory()
    requestAnimationFrame(measureFPS)

    // 定期更新内存使用情况
    const memoryInterval = setInterval(measureMemory, 5000)

    return () => {
      clearInterval(memoryInterval)
    }
  }, [])

  return metrics
}

// 懒加载Hook
export function useLazyLoad<T extends HTMLElement>(
  threshold = 0.1,
  rootMargin = "50px"
) {
  const [isInView, setIsInView] = React.useState(false)
  const [isLoaded, setIsLoaded] = React.useState(false)
  const elementRef = React.useRef<T>(null)

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isLoaded) {
          setIsInView(true)
          setIsLoaded(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )

    if (elementRef.current) {
      observer.observe(elementRef.current)
    }

    return () => observer.disconnect()
  }, [threshold, rootMargin, isLoaded])

  return { elementRef, isInView, isLoaded }
}

// 虚拟滚动Hook
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = React.useState(0)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index
  }))

  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  }
}

// 防抖Hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// 节流Hook
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = React.useState<T>(value)
  const lastRan = React.useRef(Date.now())

  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}

// 图片预加载Hook
export function useImagePreload(urls: string[]) {
  const [loadedImages, setLoadedImages] = React.useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = React.useState(false)

  const preloadImages = React.useCallback(async () => {
    setIsLoading(true)
    
    const promises = urls.map(url => {
      return new Promise<string>((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve(url)
        img.onerror = () => reject(url)
        img.src = url
      })
    })

    try {
      const loaded = await Promise.allSettled(promises)
      const successful = loaded
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<string>).value)
      
      setLoadedImages(new Set(successful))
    } catch (error) {
      console.error('图片预加载失败:', error)
    } finally {
      setIsLoading(false)
    }
  }, [urls])

  React.useEffect(() => {
    if (urls.length > 0) {
      preloadImages()
    }
  }, [urls, preloadImages])

  return { loadedImages, isLoading, preloadImages }
}

// 网络状态监控Hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )
  const [connectionType, setConnectionType] = React.useState<string>('unknown')

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 检测连接类型（如果支持）
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      setConnectionType(connection.effectiveType || 'unknown')
      
      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown')
      }
      
      connection.addEventListener('change', handleConnectionChange)
      
      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
        connection.removeEventListener('change', handleConnectionChange)
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return { isOnline, connectionType }
}

// 内存使用监控组件
export function MemoryMonitor({ children }: { children: React.ReactNode }) {
  const [memoryInfo, setMemoryInfo] = React.useState<{
    used: number
    total: number
    percentage: number
  } | null>(null)

  React.useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const used = memory.usedJSHeapSize / 1024 / 1024 // MB
        const total = memory.totalJSHeapSize / 1024 / 1024 // MB
        const percentage = (used / total) * 100

        setMemoryInfo({ used, total, percentage })

        // 如果内存使用超过80%，发出警告
        if (percentage > 80) {
          console.warn(`内存使用率过高: ${percentage.toFixed(1)}%`)
        }
      }
    }

    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 10000) // 每10秒检查一次

    return () => clearInterval(interval)
  }, [])

  return (
    <>
      {children}
      {process.env.NODE_ENV === 'development' && memoryInfo && (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-50">
          内存: {memoryInfo.used.toFixed(1)}MB / {memoryInfo.total.toFixed(1)}MB 
          ({memoryInfo.percentage.toFixed(1)}%)
        </div>
      )}
    </>
  )
}

// 性能边界组件
export interface PerformanceBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  maxRenderTime?: number
}

export function PerformanceBoundary({
  children,
  fallback,
  maxRenderTime = 16 // 60fps = 16ms per frame
}: PerformanceBoundaryProps) {
  const [isSlowRender, setIsSlowRender] = React.useState(false)
  const renderStartTime = React.useRef<number>(0)

  React.useEffect(() => {
    renderStartTime.current = performance.now()
  })

  React.useLayoutEffect(() => {
    const renderTime = performance.now() - renderStartTime.current
    if (renderTime > maxRenderTime) {
      console.warn(`慢渲染检测: ${renderTime.toFixed(2)}ms`)
      setIsSlowRender(true)
    } else {
      setIsSlowRender(false)
    }
  })

  if (isSlowRender && fallback) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
