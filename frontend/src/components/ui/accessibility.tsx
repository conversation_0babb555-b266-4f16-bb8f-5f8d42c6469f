"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// 跳转链接组件
export interface SkipLinkProps {
  href: string
  children: React.ReactNode
  className?: string
}

export function SkipLink({ href, children, className }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        "skip-link",
        "absolute -top-10 left-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-md",
        "focus:top-4 transition-all duration-200",
        "font-medium text-sm",
        className
      )}
    >
      {children}
    </a>
  )
}

// 屏幕阅读器专用文本
export interface ScreenReaderOnlyProps {
  children: React.ReactNode
  className?: string
}

export function ScreenReaderOnly({ children, className }: ScreenReaderOnlyProps) {
  return (
    <span className={cn("sr-only", className)}>
      {children}
    </span>
  )
}

// 焦点陷阱组件
export interface FocusTrapProps {
  children: React.ReactNode
  enabled?: boolean
  className?: string
}

export function FocusTrap({ children, enabled = true, className }: FocusTrapProps) {
  const containerRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    if (!enabled || !containerRef.current) return

    const container = containerRef.current
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement?.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleTabKey)
    firstElement?.focus()

    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }, [enabled])

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  )
}

// 实时区域组件（用于动态内容更新）
export interface LiveRegionProps {
  children: React.ReactNode
  politeness?: "polite" | "assertive" | "off"
  atomic?: boolean
  className?: string
}

export function LiveRegion({ 
  children, 
  politeness = "polite", 
  atomic = false,
  className 
}: LiveRegionProps) {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      className={className}
    >
      {children}
    </div>
  )
}

// 加载状态公告组件
export interface LoadingAnnouncementProps {
  isLoading: boolean
  loadingText?: string
  completedText?: string
}

export function LoadingAnnouncement({ 
  isLoading, 
  loadingText = "正在加载...", 
  completedText = "加载完成" 
}: LoadingAnnouncementProps) {
  const [announcement, setAnnouncement] = React.useState("")

  React.useEffect(() => {
    if (isLoading) {
      setAnnouncement(loadingText)
    } else {
      setAnnouncement(completedText)
      // 清除公告，避免重复
      const timer = setTimeout(() => setAnnouncement(""), 1000)
      return () => clearTimeout(timer)
    }
  }, [isLoading, loadingText, completedText])

  return (
    <LiveRegion politeness="polite">
      <ScreenReaderOnly>{announcement}</ScreenReaderOnly>
    </LiveRegion>
  )
}

// 键盘导航增强组件
export interface KeyboardNavigationProps {
  children: React.ReactNode
  onEscape?: () => void
  onEnter?: () => void
  className?: string
}

export function KeyboardNavigation({ 
  children, 
  onEscape, 
  onEnter,
  className 
}: KeyboardNavigationProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        onEscape?.()
        break
      case 'Enter':
        if (e.target === e.currentTarget) {
          onEnter?.()
        }
        break
    }
  }

  return (
    <div
      onKeyDown={handleKeyDown}
      className={className}
      tabIndex={-1}
    >
      {children}
    </div>
  )
}

// 高对比度检测Hook
export function useHighContrast() {
  const [isHighContrast, setIsHighContrast] = React.useState(false)

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)')
    setIsHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return isHighContrast
}

// 减少动画偏好检测Hook
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false)

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// 焦点管理Hook
export function useFocusManagement() {
  const [focusedElement, setFocusedElement] = React.useState<HTMLElement | null>(null)

  const saveFocus = React.useCallback(() => {
    setFocusedElement(document.activeElement as HTMLElement)
  }, [])

  const restoreFocus = React.useCallback(() => {
    if (focusedElement && document.contains(focusedElement)) {
      focusedElement.focus()
    }
  }, [focusedElement])

  const focusFirst = React.useCallback((container: HTMLElement) => {
    const focusableElement = container.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement
    focusableElement?.focus()
  }, [])

  return { saveFocus, restoreFocus, focusFirst }
}

// ARIA属性增强组件
export interface AriaEnhancedProps {
  children: React.ReactNode
  label?: string
  describedBy?: string
  expanded?: boolean
  selected?: boolean
  disabled?: boolean
  required?: boolean
  invalid?: boolean
  role?: string
  className?: string
}

export function AriaEnhanced({
  children,
  label,
  describedBy,
  expanded,
  selected,
  disabled,
  required,
  invalid,
  role,
  className,
  ...props
}: AriaEnhancedProps) {
  const ariaProps: Record<string, any> = {}

  if (label) ariaProps['aria-label'] = label
  if (describedBy) ariaProps['aria-describedby'] = describedBy
  if (expanded !== undefined) ariaProps['aria-expanded'] = expanded
  if (selected !== undefined) ariaProps['aria-selected'] = selected
  if (disabled !== undefined) ariaProps['aria-disabled'] = disabled
  if (required !== undefined) ariaProps['aria-required'] = required
  if (invalid !== undefined) ariaProps['aria-invalid'] = invalid
  if (role) ariaProps['role'] = role

  return (
    <div className={className} {...ariaProps} {...props}>
      {children}
    </div>
  )
}

// 颜色对比度检查工具
export function checkColorContrast(foreground: string, background: string): {
  ratio: number
  wcagAA: boolean
  wcagAAA: boolean
} {
  // 简化的对比度计算（实际项目中应使用更完整的实现）
  const getLuminance = (color: string) => {
    // 这里应该实现完整的亮度计算
    // 为了示例，返回一个模拟值
    return 0.5
  }

  const foregroundLum = getLuminance(foreground)
  const backgroundLum = getLuminance(background)
  
  const ratio = (Math.max(foregroundLum, backgroundLum) + 0.05) / 
                (Math.min(foregroundLum, backgroundLum) + 0.05)

  return {
    ratio,
    wcagAA: ratio >= 4.5,
    wcagAAA: ratio >= 7
  }
}
